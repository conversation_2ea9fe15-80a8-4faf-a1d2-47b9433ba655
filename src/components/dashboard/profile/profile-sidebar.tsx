'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Profile } from '@/supabase/types';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { motion } from 'framer-motion';
import {
  Calendar,
  Camera,
  CheckCircle,
  Mail,
  Shield,
  User,
} from 'lucide-react';

interface ProfileSidebarProps {
  user: SupabaseUser | null;
  profile: Profile | null;
  avatarPreview: string | null;
  onPasswordReset: () => void;
  onAvatarUpload: () => void;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ProfileSidebar({
  user,
  profile,
  avatarPreview,
  onPasswordReset,
  onAvatarUpload,
}: ProfileSidebarProps) {
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="w-80 p-8 border-l border-primary/20 bg-gradient-to-b from-muted/30 to-muted/10 backdrop-blur-xl">
      <motion.div variants={itemVariants} className="space-y-8">
        {/* User Info Card */}
        <Card className="bg-gradient-to-br from-primary/10 to-blue-500/10 border-primary/30 shadow-2xl backdrop-blur-xl">
          <CardContent className="p-8">
            <div className="text-center space-y-6">
              <div className="relative mx-auto w-24 h-24">
                <div className="w-full h-full rounded-full bg-gradient-to-br from-primary/30 to-blue-500/30 flex items-center justify-center overflow-hidden ring-4 ring-primary/20 shadow-xl">
                  {avatarPreview || profile?.avatar_url ? (
                    <img
                      src={avatarPreview || profile?.avatar_url || ''}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-12 w-12 text-primary" />
                  )}
                </div>
                <div className="absolute -bottom-1 -right-1 w-7 h-7 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-3 border-card flex items-center justify-center shadow-lg">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="space-y-3">
                <h3 className="text-xl font-bold text-foreground">
                  {profile?.full_name || 'User'}
                </h3>
                <p className="text-sm text-muted-foreground font-medium">
                  {user?.email}
                </p>
                <Badge
                  variant="secondary"
                  className="bg-gradient-to-r from-green-100 to-green-50 text-green-700 border-green-200 mt-3 px-4 py-2 shadow-md"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Verified Account
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Stats */}
        <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-base font-semibold flex items-center gap-2">
              <div className="p-1.5 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg">
                <Calendar className="h-4 w-4 text-primary" />
              </div>
              Account Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-5">
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">Member Since</span>
              <span className="text-sm font-medium text-foreground">
                {formatDate(user?.created_at)}
              </span>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">Last Updated</span>
              <span className="text-sm font-medium text-foreground">
                {formatDate(profile?.updated_at)}
              </span>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">Profile Status</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Active
              </Badge>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">Email Verified</span>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                {user?.email_confirmed_at ? 'Verified' : 'Pending'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-base font-semibold flex items-center gap-2">
              <div className="p-1.5 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg">
                <Shield className="h-4 w-4 text-primary" />
              </div>
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              variant="outline"
              size="default"
              className="w-full justify-start h-12 bg-gradient-to-r from-background/80 to-background/60 hover:from-primary/10 hover:to-primary/5 border-primary/20 hover:border-primary/30 transition-all duration-300"
              onClick={onPasswordReset}
            >
              <Mail className="h-5 w-5 mr-3" />
              Send Reset Email
            </Button>
            <Button
              variant="outline"
              size="default"
              className="w-full justify-start h-12 bg-gradient-to-r from-background/80 to-background/60 hover:from-primary/10 hover:to-primary/5 border-primary/20 hover:border-primary/30 transition-all duration-300"
              onClick={() =>
                document.getElementById('avatar-upload')?.click()
              }
            >
              <Camera className="h-5 w-5 mr-3" />
              Change Avatar
            </Button>
          </CardContent>
        </Card>

        {/* Account Security */}
        <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-base font-semibold flex items-center gap-2">
              <div className="p-1.5 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-lg">
                <Shield className="h-4 w-4 text-red-500" />
              </div>
              Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Two-Factor Auth</span>
                <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                  Disabled
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Login Sessions</span>
                <span className="text-sm font-medium text-foreground">1 Active</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Last Login</span>
                <span className="text-sm font-medium text-foreground">
                  {formatDate(user?.last_sign_in_at)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
