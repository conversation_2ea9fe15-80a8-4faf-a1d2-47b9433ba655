'use client';

import { motion } from 'framer-motion';

interface ProfileHeaderProps {
  title?: string;
  description?: string;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ProfileHeader({ 
  title = "Profile Settings", 
  description = "Manage your account settings and preferences with our enhanced interface." 
}: ProfileHeaderProps) {
  return (
    <motion.div variants={itemVariants} className="mb-10">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-2xl blur-3xl"></div>
        <div className="relative bg-gradient-to-r from-card/80 to-card/60 backdrop-blur-xl rounded-2xl border border-primary/20 p-8 shadow-xl">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent mb-3">
            {title}
          </h1>
          <p className="text-muted-foreground text-lg">
            {description}
          </p>
        </div>
      </div>
    </motion.div>
  );
}
