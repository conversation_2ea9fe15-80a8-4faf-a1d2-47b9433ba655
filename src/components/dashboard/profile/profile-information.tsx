'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Profile, UpdateProfile } from '@/supabase/types';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { motion } from 'framer-motion';
import {
  Camera,
  Eye,
  EyeOff,
  Lock,
  Save,
  Upload,
  User,
} from 'lucide-react';
import { ChangeEvent, FormEvent } from 'react';

interface ProfileFormData {
  full_name: string;
  email: string;
  username: string;
  website: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ShowPasswordsState {
  current: boolean;
  new: boolean;
  confirm: boolean;
}

interface ProfileInformationProps {
  user: SupabaseUser | null;
  profile: Profile | null;
  profileForm: ProfileFormData;
  passwordForm: PasswordFormData;
  showPasswords: ShowPasswordsState;
  avatarFile: File | null;
  avatarPreview: string | null;
  isUpdatingProfile: boolean;
  isChangingPassword: boolean;
  isUploadingAvatar: boolean;
  onProfileFormChange: (field: keyof ProfileFormData, value: string) => void;
  onPasswordFormChange: (field: keyof PasswordFormData, value: string) => void;
  onShowPasswordToggle: (field: keyof ShowPasswordsState) => void;
  onProfileSubmit: (e: FormEvent) => void;
  onPasswordSubmit: (e: FormEvent) => void;
  onAvatarFileChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onAvatarUpload: () => void;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ProfileInformation({
  user,
  profile,
  profileForm,
  passwordForm,
  showPasswords,
  avatarFile,
  avatarPreview,
  isUpdatingProfile,
  isChangingPassword,
  isUploadingAvatar,
  onProfileFormChange,
  onPasswordFormChange,
  onShowPasswordToggle,
  onProfileSubmit,
  onPasswordSubmit,
  onAvatarFileChange,
  onAvatarUpload,
}: ProfileInformationProps) {
  return (
    <motion.div variants={itemVariants}>
      <Tabs defaultValue="profile" className="space-y-8">
        <TabsList className="grid w-full grid-cols-3 max-w-lg h-14 bg-gradient-to-r from-muted/50 to-muted/30 backdrop-blur-xl border border-border/50 rounded-xl p-1">
          <TabsTrigger
            value="profile"
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-primary/80 data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg transition-all duration-300"
          >
            <User className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-primary/80 data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg transition-all duration-300"
          >
            <Lock className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger 
            value="avatar" 
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-primary/80 data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg transition-all duration-300"
          >
            <Camera className="h-4 w-4" />
            Avatar
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-8">
          <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg">
                  <User className="h-5 w-5 text-primary" />
                </div>
                Personal Information
              </CardTitle>
              <CardDescription className="text-base">
                Update your personal details and contact information to keep your profile current.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={onProfileSubmit} className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="full_name">Full Name</Label>
                    <Input
                      id="full_name"
                      value={profileForm.full_name}
                      onChange={e => onProfileFormChange('full_name', e.target.value)}
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      value={profileForm.username}
                      onChange={e => onProfileFormChange('username', e.target.value)}
                      placeholder="Choose a username"
                    />
                  </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profileForm.email}
                      onChange={e => onProfileFormChange('email', e.target.value)}
                      placeholder="Enter your email"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      type="url"
                      value={profileForm.website}
                      onChange={e => onProfileFormChange('website', e.target.value)}
                      placeholder="https://yourwebsite.com"
                    />
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button
                    type="submit"
                    disabled={isUpdatingProfile}
                    className="px-8 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isUpdatingProfile ? 'Updating...' : 'Update Profile'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-8">
          <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg">
                  <Lock className="h-5 w-5 text-primary" />
                </div>
                Change Password
              </CardTitle>
              <CardDescription className="text-base">
                Update your password to keep your account secure.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={onPasswordSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <div className="relative">
                    <Input
                      id="currentPassword"
                      type={showPasswords.current ? 'text' : 'password'}
                      value={passwordForm.currentPassword}
                      onChange={e => onPasswordFormChange('currentPassword', e.target.value)}
                      placeholder="Enter current password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => onShowPasswordToggle('current')}
                    >
                      {showPasswords.current ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showPasswords.new ? 'text' : 'password'}
                        value={passwordForm.newPassword}
                        onChange={e => onPasswordFormChange('newPassword', e.target.value)}
                        placeholder="Enter new password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => onShowPasswordToggle('new')}
                      >
                        {showPasswords.new ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showPasswords.confirm ? 'text' : 'password'}
                        value={passwordForm.confirmPassword}
                        onChange={e => onPasswordFormChange('confirmPassword', e.target.value)}
                        placeholder="Confirm new password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => onShowPasswordToggle('confirm')}
                      >
                        {showPasswords.confirm ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button
                    type="submit"
                    disabled={isChangingPassword}
                    className="px-8 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <Lock className="h-4 w-4 mr-2" />
                    {isChangingPassword ? 'Changing...' : 'Change Password'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Avatar Tab */}
        <TabsContent value="avatar" className="space-y-8">
          <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg">
                  <Camera className="h-5 w-5 text-primary" />
                </div>
                Profile Picture
              </CardTitle>
              <CardDescription className="text-base">
                Upload a new profile picture to personalize your account.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center gap-6">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 flex items-center justify-center overflow-hidden">
                    {avatarPreview || profile?.avatar_url ? (
                      <img
                        src={avatarPreview || profile?.avatar_url || ''}
                        alt="Profile"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User className="h-12 w-12 text-primary" />
                    )}
                  </div>
                  <div className="space-y-4">
                    <div>
                      <input
                        id="avatar-upload"
                        type="file"
                        accept="image/*"
                        onChange={onAvatarFileChange}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('avatar-upload')?.click()}
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </Button>
                    </div>
                    {avatarFile && (
                      <Button
                        onClick={onAvatarUpload}
                        disabled={isUploadingAvatar}
                        className="shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        <Camera className="h-4 w-4 mr-2" />
                        {isUploadingAvatar ? 'Uploading...' : 'Upload Avatar'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
}
