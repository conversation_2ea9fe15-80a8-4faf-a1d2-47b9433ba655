'use client';

import { Badge } from '@/components/ui/badge';
import { CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { Brain, Rocket, Sparkles, Star, TrendingUp, Zap } from 'lucide-react';
import { DashboardSection } from './dashboard-section';
import { FeatureCard } from './feature-card';

interface SEO45HeroProps {
  websiteCount: number;
  articleCount: number;
  onAddWebsite: () => void;
}

export const SEO45Hero: React.FC<SEO45HeroProps> = ({
  websiteCount,
  onAddWebsite,
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const floatingVariants = {
    animate: {
      y: [0, -8, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative overflow-hidden bg-gradient-to-br from-primary/5 via-white to-blue-50  rounded-3xl border border-primary/20 dark:border-zinc-800 dark:border-primary/30  dark:from-zinc-950  dark:via-slate-900 dark:to-primary/20  "
    >
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0 opacity-30 dark:opacity-20">
        <div className="absolute inset-0 bg-primary/5 dark:bg-primary/10">
          <div className="absolute top-10 left-10 w-1 h-1 bg-primary/60 dark:bg-primary/80 rounded-full"></div>
          <div className="absolute top-20 left-40 w-1.5 h-1.5 bg-primary/40 dark:bg-primary/60 rounded-full"></div>
          <div className="absolute top-40 left-20 w-1 h-1 bg-primary/50 dark:bg-primary/70 rounded-full"></div>
          <div className="absolute top-60 left-60 w-2 h-2 bg-primary/35 dark:bg-primary/55 rounded-full"></div>
          <div className="absolute top-80 left-80 w-1 h-1 bg-primary/55 dark:bg-primary/75 rounded-full"></div>
        </div>
      </div>

      {/* Animated Gradient Overlays */}
      <motion.div
        animate={{
          x: [0, 30, 0],
          y: [0, -25, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-primary/15 to-primary/5 dark:from-primary/25 dark:to-primary/10 rounded-full blur-3xl transform translate-x-48 -translate-y-48"
      />
      <motion.div
        animate={{
          x: [0, -25, 0],
          y: [0, 30, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-primary/12 to-primary/3 dark:from-primary/20 dark:to-primary/8 rounded-full blur-3xl transform -translate-x-48 translate-y-48"
      />

      {/* Additional floating elements */}
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute top-24 left-16 w-2 h-2 bg-primary/40 dark:bg-primary/60 rounded-full blur-sm"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        transition={{ delay: 1.5 }}
        className="absolute top-40 right-24 w-3 h-3 bg-primary/30 dark:bg-primary/50 rounded-full blur-sm"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        transition={{ delay: 3 }}
        className="absolute bottom-32 left-20 w-2.5 h-2.5 bg-primary/35 dark:bg-primary/55 rounded-full blur-sm"
      />

      <CardContent className="relative px-8 py-12 md:p-12 lg:px-12 lg:py-10">
        <div className="flex flex-col md:flex-row md:gap-12 lg:gap-16 items-start">
          {/* Left Content */}
          <div className="w-full space-y-8 flex-[60%]">
            {/* Brand Section */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="relative group cursor-pointer">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 rounded-2xl blur-lg opacity-50 group-hover:opacity-70 transition-all duration-300" />
                  <div className="relative p-4 bg-primary/10 dark:bg-primary/20 backdrop-blur-xl rounded-2xl border border-primary/20 dark:border-primary/30 group-hover:border-primary/40 transition-all duration-300 group-hover:scale-105 group-hover:rotate-2 group-hover:shadow-lg">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{
                        duration: 25,
                        repeat: Infinity,
                        ease: 'linear',
                      }}
                    >
                      <Sparkles className="h-10 w-10 text-primary group-hover:text-primary/80 transition-colors duration-300" />
                    </motion.div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <motion.h1
                      variants={itemVariants}
                      className="text-5xl md:text-6xl lg:text-7xl  text-slate-800 dark:text-slate-100 tracking-tight font-extrabold"
                    >
                      SEO45
                    </motion.h1>
                  </div>
                  <motion.div
                    variants={itemVariants}
                    className="flex items-center gap-2"
                  >
                    <Badge variant={'default'}>
                      <Rocket className="h-3 w-3 mr-1.5" />
                      Next-Gen AI Platform
                    </Badge>

                    <motion.div variants={itemVariants} className="flex">
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.1 * i, duration: 0.3 }}
                          className="hover:scale-125 hover:rotate-12 transition-all duration-200 cursor-pointer"
                        >
                          <Star className="h-4 w-4 lg:w-5 lg:h-5 text-yellow-500 fill-current" />
                        </motion.div>
                      ))}
                    </motion.div>
                  </motion.div>
                </div>
              </div>

              <motion.div variants={itemVariants} className="space-y-4">
                <h2 className="text-2xl md:text-3xl font-bold text-slate-800 dark:text-slate-200 leading-tight">
                  Revolutionary AI-Powered SEO & Content Automation
                </h2>
                <p className="text-slate-600 dark:text-slate-400 text-lg leading-relaxed max-w-xl">
                  Experience the future of digital marketing with our
                  cutting-edge AI that creates, optimizes, and manages your
                  content while you sleep.
                </p>
              </motion.div>
            </motion.div>

            {/* Feature Highlights */}
            <motion.div variants={itemVariants} className="space-y-3">
              <FeatureCard
                icon={Zap}
                title="Lightning-Fast Content Generation"
                description="AI creates optimized content in seconds"
                gradient="from-primary to-primary/80"
                delay={0.2}
              />
              <FeatureCard
                icon={TrendingUp}
                title="Advanced SEO Intelligence"
                description="Automatic ranking optimization & keyword research"
                gradient="from-primary/90 to-primary/70"
                delay={0.3}
              />
              <FeatureCard
                icon={Brain}
                title="Smart Content Strategy"
                description="AI-driven content planning and optimization"
                gradient="from-primary/80 to-primary/60"
                delay={0.4}
              />
            </motion.div>
          </div>

          {/* Right Dashboard Section */}
          <div className="w-full flex-[40%]">
            <DashboardSection
              websiteCount={websiteCount}
              onAddWebsite={onAddWebsite}
            />
          </div>
        </div>
      </CardContent>
    </motion.div>
  );
};
