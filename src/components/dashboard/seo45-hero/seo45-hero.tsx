'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import {
  ArrowRight,
  Brain,
  Globe,
  Rocket,
  Sparkles,
  Star,
  TrendingUp,
  Zap,
} from 'lucide-react';
import { FeatureCard } from './feature-card';

interface SEO45HeroProps {
  websiteCount: number;
  articleCount: number;
  onAddWebsite: () => void;
}

export const SEO45Hero: React.FC<SEO45HeroProps> = ({
  websiteCount,
  onAddWebsite,
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const floatingVariants = {
    animate: {
      y: [0, -8, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative overflow-hidden bg-gradient-to-br from-primary/8 via-background to-primary/4 rounded-3xl border border-primary/20 dark:border-primary/30 shadow-2xl"
    >
      {/* Modern Background Pattern */}
      <div className="absolute inset-0">
        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:14px_24px] opacity-20"></div>

        {/* Floating Orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-full blur-2xl"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-br from-purple-500/20 to-primary/20 rounded-full blur-2xl"
        />
      </div>

      {/* Subtle floating elements */}
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute top-32 left-24 w-2 h-2 bg-primary/50 rounded-full"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        transition={{ delay: 1.5 }}
        className="absolute top-48 right-32 w-1.5 h-1.5 bg-blue-500/50 rounded-full"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        transition={{ delay: 3 }}
        className="absolute bottom-40 left-32 w-2.5 h-2.5 bg-purple-500/50 rounded-full"
      />

      <CardContent className="relative px-8 py-16 md:p-16 lg:px-16 lg:py-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <motion.div variants={itemVariants} className="space-y-8">
              {/* Brand Section */}
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <motion.div
                    className="relative p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl border border-primary/30 shadow-lg"
                    whileHover={{ scale: 1.05, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Sparkles className="h-12 w-12 text-primary" />
                  </motion.div>
                  <div>
                    <motion.h1
                      variants={itemVariants}
                      className="text-6xl md:text-7xl lg:text-8xl font-black bg-gradient-to-r from-primary via-primary/80 to-blue-600 bg-clip-text text-transparent tracking-tight"
                    >
                      SEO45
                    </motion.h1>
                    <div className="flex items-center gap-3 mt-2">
                      <Badge variant="default" className="px-3 py-1">
                        <Rocket className="h-3 w-3 mr-1.5" />
                        AI-Powered Platform
                      </Badge>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="h-4 w-4 text-yellow-500 fill-current"
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground leading-tight">
                    Transform Your Content Strategy with{' '}
                    <span className="bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                      AI Automation
                    </span>
                  </h2>
                  <p className="text-muted-foreground text-xl leading-relaxed max-w-2xl">
                    Create, optimize, and publish SEO-perfect content
                    automatically. Our AI handles everything from keyword
                    research to content creation, so you can focus on growing
                    your business.
                  </p>
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-4">
                  <Button
                    size="lg"
                    className="px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                    onClick={onAddWebsite}
                  >
                    <Globe className="h-5 w-5 mr-2" />
                    Add Your Website
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="px-8 py-4 text-lg font-semibold border-2 hover:bg-primary/5"
                  >
                    Watch Demo
                  </Button>
                </div>
              </div>
            </motion.div>

            {/* Right Features Grid */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="grid gap-6">
                <FeatureCard
                  icon={Zap}
                  title="Lightning-Fast Generation"
                  description="Create SEO-optimized articles in under 60 seconds"
                  gradient="from-yellow-500 to-orange-500"
                  delay={0.2}
                />
                <FeatureCard
                  icon={TrendingUp}
                  title="Smart SEO Optimization"
                  description="Automatic keyword research and ranking optimization"
                  gradient="from-green-500 to-emerald-500"
                  delay={0.3}
                />
                <FeatureCard
                  icon={Brain}
                  title="Intelligent Content Strategy"
                  description="AI-driven planning and content calendar management"
                  gradient="from-purple-500 to-indigo-500"
                  delay={0.4}
                />
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-6 pt-6">
                <div className="text-center p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl border border-primary/20">
                  <div className="text-3xl font-bold text-primary">
                    {websiteCount}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Active Websites
                  </div>
                </div>
                <div className="text-center p-6 bg-gradient-to-br from-green-500/10 to-green-500/5 rounded-2xl border border-green-500/20">
                  <div className="text-3xl font-bold text-green-600">24/7</div>
                  <div className="text-sm text-muted-foreground">
                    Auto Publishing
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </CardContent>
    </motion.div>
  );
};
