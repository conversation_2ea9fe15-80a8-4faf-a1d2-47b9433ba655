import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { getAllPlans } from '@/services/plan/plan-service';
import { getWebsitesByUser } from '@/services/website/website-service';
import { useAuth } from '@/stores';
import { Plan, Website } from '@/supabase/types';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  AlertTriangle,
  ArrowRight,
  Check,
  Crown,
  ExternalLink,
  Globe,
  Plus,
  Settings,
} from 'lucide-react';
import { useState } from 'react';

// Extended Website type with subscription info
interface WebsiteWithSubscription extends Website {
  subscription?: {
    id: number;
    plan_id: number;
    status: string;
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    plan: Plan;
  };
}

export default function WebsiteSubscriptionManagement() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedWebsite, setSelectedWebsite] =
    useState<WebsiteWithSubscription | null>(null);
  const [showPlanDialog, setShowPlanDialog] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  // Fetch user's websites
  const {
    data: websites = [],
    isLoading: websitesLoading,
    error: websitesError,
  } = useQuery({
    queryKey: ['websites', user?.id],
    queryFn: () => {
      if (!user?.id) throw new Error('User not authenticated');
      return getWebsitesByUser(user.id);
    },
    enabled: !!user?.id,
  });

  // Fetch available plans
  const {
    data: plans = [],
    isLoading: plansLoading,
    error: plansError,
  } = useQuery({
    queryKey: ['plans'],
    queryFn: getAllPlans,
  });

  // Mock subscription data for each website (you'll need to implement actual subscription fetching)
  const websitesWithSubscriptions: WebsiteWithSubscription[] = websites.map(
    website => ({
      ...website,
      subscription:
        Math.random() > 0.5
          ? {
              id: Math.floor(Math.random() * 1000),
              plan_id: plans[Math.floor(Math.random() * plans.length)]?.id || 1,
              status: ['active', 'trial', 'cancelled'][
                Math.floor(Math.random() * 3)
              ],
              current_period_start: new Date().toISOString(),
              current_period_end: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              cancel_at_period_end: Math.random() > 0.8,
              plan: plans[Math.floor(Math.random() * plans.length)] || {
                id: 1,
                name: 'Basic',
                price: 29,
                interval: 'month',
                features: ['Basic features'],
                description: 'Basic plan',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              },
            }
          : undefined,
    })
  );

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'trial':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'cancelled':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (websitesLoading || plansLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <Skeleton className="h-8 w-64 mx-auto mb-2" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (websitesError || plansError) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertTriangle className="mx-auto h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Error Loading Data
          </h3>
          <p className="text-muted-foreground mb-4">
            {websitesError?.message || plansError?.message}
          </p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Subscriptions
            </h1>
            <p className="text-muted-foreground text-lg">
              Manage subscriptions for each of your websites
            </p>
          </div>
          <Button
            variant="outline"
            size="lg"
            className="w-full lg:w-auto shadow-md hover:shadow-lg transition-all duration-200"
            onClick={() => setShowPlanDialog(true)}
          >
            <Plus className="mr-2 h-5 w-5" />
            Add Subscription
          </Button>
        </div>
      </motion.div>

      {/* Website Cards */}
      <motion.div variants={itemVariants}>
        {websites.length === 0 ? (
          <Card className="text-center py-16 border-2 border-dashed border-muted-foreground/20">
            <CardContent>
              <div className="space-y-6">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur-2xl"></div>
                  <div className="relative p-6 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full w-32 h-32 mx-auto flex items-center justify-center">
                    <Globe className="h-16 w-16 text-primary" />
                  </div>
                </div>
                <div className="space-y-3">
                  <h3 className="text-2xl font-bold text-foreground">
                    No websites yet
                  </h3>
                  <p className="text-muted-foreground max-w-md mx-auto text-lg">
                    Add your first website to start managing subscriptions and
                    automated content creation.
                  </p>
                </div>
                <Button
                  size="lg"
                  className="font-medium px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Add Your First Website
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {websitesWithSubscriptions.map((website, index) => (
              <motion.div
                key={website.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -4 }}
              >
                <Card className="h-full hover:shadow-xl transition-all duration-300 hover:border-primary/30 border-border/50 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        <motion.div
                          className="relative p-3 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border border-primary/20"
                          whileHover={{ scale: 1.05, rotate: 5 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Globe className="h-6 w-6 text-primary" />
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-card"></div>
                        </motion.div>
                        <div className="min-w-0 flex-1">
                          <h3 className="font-bold text-lg text-foreground truncate mb-1">
                            {website.domain_name}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <ExternalLink className="h-4 w-4" />
                            <span className="truncate hover:text-primary transition-colors cursor-pointer">
                              {website.website_url}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-1">
                        <Badge
                          className={`font-medium px-3 py-1 ${getStatusColor(website.status || 'inactive')}`}
                        >
                          {website.status || 'inactive'}
                        </Badge>
                        {website.subscription ? (
                          <Badge
                            className={`font-medium px-3 py-1 ${getStatusColor(website.subscription.status)} text-xs`}
                          >
                            {website.subscription.status}
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="bg-gray-100 text-gray-700 border-gray-200 text-xs"
                          >
                            No Plan
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Subscription Info */}
                    <div className="space-y-3">
                      {website.subscription ? (
                        <>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              Plan:
                            </span>
                            <span className="font-semibold text-foreground">
                              {website.subscription.plan.name}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              Price:
                            </span>
                            <span className="font-semibold text-foreground">
                              {formatPrice(website.subscription.plan.price)}/
                              {website.subscription.plan.interval}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              Next billing:
                            </span>
                            <span className="font-semibold text-foreground">
                              {formatDate(
                                website.subscription.current_period_end
                              )}
                            </span>
                          </div>
                          {website.subscription.cancel_at_period_end && (
                            <div className="flex items-center gap-2 text-sm text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20 p-2 rounded-lg">
                              <AlertTriangle className="h-4 w-4" />
                              <span>Cancelling at period end</span>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-center py-4">
                          <p className="text-sm text-muted-foreground mb-2">
                            No active subscription
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Subscribe to enable automated content creation
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      {website.subscription ? (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 hover:bg-primary/10 hover:border-primary/30 transition-all duration-200"
                            onClick={() => setSelectedWebsite(website)}
                          >
                            <Settings className="h-4 w-4 mr-2" />
                            Manage
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700 transition-all duration-200"
                            onClick={() => {
                              setSelectedWebsite(website);
                              setShowPlanDialog(true);
                            }}
                          >
                            <ArrowRight className="h-4 w-4 mr-2" />
                            Upgrade
                          </Button>
                        </>
                      ) : (
                        <Button
                          className="w-full shadow-md hover:shadow-lg transition-all duration-200"
                          onClick={() => {
                            setSelectedWebsite(website);
                            setShowPlanDialog(true);
                          }}
                        >
                          <Crown className="h-4 w-4 mr-2" />
                          Subscribe Now
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Plan Selection Dialog */}
      <Dialog open={showPlanDialog} onOpenChange={setShowPlanDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl">
              {selectedWebsite?.subscription ? 'Upgrade Plan' : 'Choose a Plan'}{' '}
              for {selectedWebsite?.domain_name}
            </DialogTitle>
            <DialogDescription>
              Select the perfect plan for your website's content needs and
              growth goals.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
            {plans.map(plan => (
              <Card
                key={plan.id}
                className={`relative hover:shadow-lg transition-all duration-200 ${
                  plan.name.toLowerCase().includes('pro')
                    ? 'border-primary/50 bg-gradient-to-br from-primary/5 to-primary/10'
                    : 'hover:border-primary/30'
                }`}
              >
                {plan.name.toLowerCase().includes('pro') && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground font-medium px-3 py-1">
                      <Crown className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <div className="text-3xl font-bold text-foreground">
                    {formatPrice(plan.price)}
                    <span className="text-sm font-normal text-muted-foreground">
                      /{plan.interval}
                    </span>
                  </div>
                  {plan.description && (
                    <p className="text-sm text-muted-foreground">
                      {plan.description}
                    </p>
                  )}
                </CardHeader>

                <CardContent className="space-y-4">
                  {plan.features && (
                    <ul className="space-y-2">
                      {(Array.isArray(plan.features)
                        ? plan.features
                        : typeof plan.features === 'string'
                          ? JSON.parse(plan.features)
                          : []
                      ).map((feature: string, index: number) => (
                        <li key={index} className="flex items-center text-sm">
                          <Check className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  )}

                  <Button
                    className="w-full"
                    variant={
                      plan.name.toLowerCase().includes('pro')
                        ? 'default'
                        : 'outline'
                    }
                    onClick={() => {
                      // Handle subscription logic here
                      toast({
                        title: 'Subscription Updated',
                        description: `Successfully subscribed ${selectedWebsite?.domain_name} to ${plan.name} plan.`,
                      });
                      setShowPlanDialog(false);
                    }}
                  >
                    {selectedWebsite?.subscription
                      ? 'Upgrade to'
                      : 'Subscribe to'}{' '}
                    {plan.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}
