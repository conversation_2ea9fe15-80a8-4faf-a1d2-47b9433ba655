import { SEO45Hero } from '@/components/dashboard/seo45-hero';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { WebsiteCard } from '@/components/website/website-card';
import { Subscription, Website } from '@/supabase/types';
import { motion } from 'framer-motion';
import { Activity, Calendar, FileText, Globe, Plus } from 'lucide-react';

interface OverviewTabProps {
  websites: Website[];
  subscriptions: Subscription[];
  loading?: boolean;
  onAddWebsite: () => void;
}

export const OverviewTab: React.FC<OverviewTabProps> = ({
  websites,
  subscriptions,
  loading,
  onAddWebsite,
}) => {
  // Calculate stats
  const activeWebsites = websites.filter(w => w.status === 'active').length;
  const activeSubscriptions = subscriptions.filter(
    s => s.status === 'active'
  ).length;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* SEO45 Hero */}
      <motion.div variants={itemVariants}>
        <SEO45Hero
          websiteCount={websites.length}
          articleCount={0}
          onAddWebsite={onAddWebsite}
        />
      </motion.div>

      {/* Quick Stats Cards */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Websites
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {websites.length}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                  <Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-green-600 dark:text-green-400 font-medium">
                  {activeWebsites} active
                </span>
                <span className="text-muted-foreground ml-1">websites</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Active Subscriptions
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {activeSubscriptions}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full">
                  <Activity className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <div className="mt-2 flex items-center text-sm">
                <Calendar className="h-4 w-4 text-purple-600 dark:text-purple-400 mr-1" />
                <span className="text-muted-foreground">
                  Subscription management
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Content Status
                  </p>
                  <p className="text-2xl font-bold text-foreground">Active</p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full">
                  <FileText className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-green-600 dark:text-green-400 font-medium">
                  Auto-publishing enabled
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Websites Section */}
      <motion.div variants={itemVariants}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-foreground">
              Your Websites
            </h2>
            <p className="text-muted-foreground mt-1">
              Manage and monitor your connected websites
            </p>
          </div>
          <Button
            onClick={onAddWebsite}
            variant="default"
            size="default"
            className="font-medium shadow-md hover:shadow-lg transition-shadow duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Website
          </Button>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map(i => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3 flex-1">
                      <div className="w-12 h-12 bg-muted rounded-xl"></div>
                      <div className="flex-1">
                        <div className="h-5 bg-muted rounded w-2/3 mb-2"></div>
                        <div className="h-4 bg-muted rounded w-1/2"></div>
                      </div>
                    </div>
                    <div className="h-6 bg-muted rounded-full w-20"></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="h-20 bg-muted rounded-xl"></div>
                    <div className="h-20 bg-muted rounded-xl"></div>
                  </div>
                  <div className="space-y-3">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-4/5"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : websites.length === 0 ? (
          <Card className="text-center py-16 border-2 border-dashed border-muted-foreground/20 hover:border-primary/30 transition-colors duration-200">
            <CardContent>
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur-2xl"></div>
                  <div className="relative p-6 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full w-32 h-32 mx-auto flex items-center justify-center">
                    <Globe className="h-16 w-16 text-primary" />
                  </div>
                </div>
                <div className="space-y-3">
                  <h3 className="text-2xl font-bold text-foreground">
                    Ready to get started?
                  </h3>
                  <p className="text-muted-foreground max-w-md mx-auto text-lg">
                    Add your first website to begin automated content creation
                    and SEO optimization with our AI-powered platform.
                  </p>
                </div>
                <div className="space-y-4">
                  <Button
                    onClick={onAddWebsite}
                    size="lg"
                    className="font-medium px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Add Your First Website
                  </Button>
                  <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>AI-Powered</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>SEO Optimized</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span>Automated</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {websites.map((website, index) => (
              <motion.div
                key={website.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <WebsiteCard
                  website={website}
                  subscriptions={subscriptions}
                  showArticleCount={true}
                  className="h-full hover:scale-[1.02] transition-transform duration-200"
                />
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Recent Activity Section */}
      {websites.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {websites.slice(0, 3).map(website => (
                  <div
                    key={website.id}
                    className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg"
                  >
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <FileText className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-foreground">
                        New article published on {website.domain_name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {Math.floor(Math.random() * 24)} hours ago
                      </p>
                    </div>
                    <Badge variant="secondary">Published</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
};
