import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WebsiteCard } from '@/components/website/website-card';
import { Website } from '@/supabase/types';
import { motion } from 'framer-motion';
import { Filter, Globe, Grid, List, Plus, Search } from 'lucide-react';
import React from 'react';

interface WebsiteGridProps {
  websites: Website[];
  loading?: boolean;
  onAddWebsite: () => void;
}

export const WebsiteGrid: React.FC<WebsiteGridProps> = ({
  websites,
  loading,
  onAddWebsite,
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState('all');
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');

  // Filter websites based on search and status
  const filteredWebsites = websites.filter(website => {
    const matchesSearch =
      website.domain_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      website.website_url?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || website.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Your Websites
            </h1>
            <p className="text-muted-foreground text-lg">
              Manage your websites and monitor their performance
            </p>
          </div>
          <Button
            onClick={onAddWebsite}
            size="lg"
            className="w-full lg:w-auto shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Plus className="mr-2 h-5 w-5" />
            Add Website
          </Button>
        </div>
      </motion.div>

      {/* Filters and Search */}
      <motion.div variants={itemVariants}>
        <Card className="p-6">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search websites..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                />
              </div>

              {/* Status Filter */}
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <select
                  value={statusFilter}
                  onChange={e => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="setup-pending">Setup Pending</option>
                </select>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2 bg-muted/50 rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-8 w-8 p-0"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className="flex items-center gap-6 mt-4 pt-4 border-t border-border/50">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Globe className="h-4 w-4" />
              <span>
                {filteredWebsites.length} of {websites.length} websites
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>
                {websites.filter(w => w.status === 'active').length} active
              </span>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Content Section */}
      <motion.div variants={itemVariants}>
        {loading ? (
          <div
            className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'}`}
          >
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center gap-4 flex-1">
                      <div className="w-12 h-12 bg-muted rounded-xl"></div>
                      <div className="flex-1">
                        <div className="h-6 bg-muted rounded w-2/3 mb-2"></div>
                        <div className="h-4 bg-muted rounded w-1/2"></div>
                      </div>
                    </div>
                    <div className="h-6 bg-muted rounded-full w-24"></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="h-20 bg-muted rounded-xl"></div>
                    <div className="h-20 bg-muted rounded-xl"></div>
                  </div>
                  <div className="space-y-4">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-4/5"></div>
                    <div className="flex gap-2">
                      <div className="h-8 bg-muted rounded flex-1"></div>
                      <div className="h-8 bg-muted rounded flex-1"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredWebsites.length === 0 ? (
          <Card className="text-center py-16 border-2 border-dashed border-muted-foreground/20">
            <CardContent>
              <div className="space-y-6">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur-2xl"></div>
                  <div className="relative p-6 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full w-32 h-32 mx-auto flex items-center justify-center">
                    <Globe className="h-16 w-16 text-primary" />
                  </div>
                </div>
                <div className="space-y-3">
                  <h3 className="text-2xl font-bold text-foreground">
                    {searchTerm || statusFilter !== 'all'
                      ? 'No websites found'
                      : 'No websites yet'}
                  </h3>
                  <p className="text-muted-foreground max-w-md mx-auto text-lg">
                    {searchTerm || statusFilter !== 'all'
                      ? 'Try adjusting your search or filter criteria'
                      : 'Add your first website to start automated content creation and SEO optimization'}
                  </p>
                </div>
                {!searchTerm && statusFilter === 'all' && (
                  <Button
                    onClick={onAddWebsite}
                    size="lg"
                    className="font-medium px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Add Your First Website
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div
            className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'}`}
          >
            {filteredWebsites.map((website: Website, index) => (
              <motion.div
                key={website.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <WebsiteCard
                  website={website}
                  showArticleCount={true}
                  className="h-full hover:scale-[1.02] transition-transform duration-200"
                />
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};
