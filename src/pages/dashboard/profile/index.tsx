'use client';

import { <PERSON>Header, ProfileInformation, ProfileSidebar } from '@/components/dashboard/profile';
import { useToast } from '@/hooks/use-toast';
import { updateProfile, uploadAvatar } from '@/services/profile/profile-service';
import { useAuth } from '@/stores';
import { Profile, UpdateProfile } from '@/supabase/types';
import { createClient } from '@/supabase/client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { ChangeEvent, FormEvent, useEffect, useState } from 'react';

interface ProfileFormData {
  full_name: string;
  email: string;
  username: string;
  website: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ShowPasswordsState {
  current: boolean;
  new: boolean;
  confirm: boolean;
}

export default function ProfilePage() {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const supabase = createClient();

  // Form states
  const [profileForm, setProfileForm] = useState<ProfileFormData>({
    full_name: profile?.full_name || '',
    email: profile?.email || user?.email || '',
    username: profile?.username || '',
    website: profile?.website || '',
  });

  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [showPasswords, setShowPasswords] = useState<ShowPasswordsState>({
    current: false,
    new: false,
    confirm: false,
  });

  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Update form when profile changes
  useEffect(() => {
    if (profile) {
      setProfileForm({
        full_name: profile.full_name || '',
        email: profile.email || user?.email || '',
        username: profile.username || '',
        website: profile.website || '',
      });
    }
  }, [profile, user?.email]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  // Profile update mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormData) => {
      if (!user?.id) throw new Error('User not authenticated');
      const updateData: UpdateProfile = {
        ...data,
        updated_at: new Date().toISOString(),
      };
      return updateProfile(user.id, updateData);
    },
    onSuccess: () => {
      toast({
        title: 'Profile Updated',
        description: 'Your profile has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Update Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Password change mutation
  const changePasswordMutation = useMutation({
    mutationFn: async (data: PasswordFormData) => {
      if (data.newPassword !== data.confirmPassword) {
        throw new Error('New passwords do not match');
      }

      const { error } = await supabase.auth.updateUser({
        password: data.newPassword,
      });

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Password Changed',
        description: 'Your password has been changed successfully.',
      });
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Password Change Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Avatar upload mutation
  const uploadAvatarMutation = useMutation({
    mutationFn: async (file: File) => {
      if (!user?.id) throw new Error('User not authenticated');

      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const {
        data: { publicUrl },
      } = supabase.storage.from('avatars').getPublicUrl(filePath);

      const updateData: UpdateProfile = {
        avatar_url: publicUrl,
        updated_at: new Date().toISOString(),
      };
      return updateProfile(user.id, updateData);
    },
    onSuccess: () => {
      toast({
        title: 'Avatar Updated',
        description: 'Your profile picture has been updated successfully.',
      });
      setAvatarFile(null);
      setAvatarPreview(null);
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Upload Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Form handlers
  const handleProfileFormChange = (field: keyof ProfileFormData, value: string) => {
    setProfileForm(prev => ({ ...prev, [field]: value }));
  };

  const handlePasswordFormChange = (field: keyof PasswordFormData, value: string) => {
    setPasswordForm(prev => ({ ...prev, [field]: value }));
  };

  const handleShowPasswordToggle = (field: keyof ShowPasswordsState) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const handleProfileSubmit = (e: FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate(profileForm);
  };

  const handlePasswordSubmit = (e: FormEvent) => {
    e.preventDefault();
    changePasswordMutation.mutate(passwordForm);
  };

  const handleAvatarFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAvatarUpload = () => {
    if (avatarFile) {
      uploadAvatarMutation.mutate(avatarFile);
    }
  };

  const handlePasswordReset = async () => {
    if (!user?.email) return;

    const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    if (error) {
      toast({
        title: 'Reset Failed',
        description: error.message,
        variant: 'destructive',
      });
    } else {
      toast({
        title: 'Reset Email Sent',
        description: 'Check your email for password reset instructions.',
      });
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5"
    >
      <div className="flex h-full">
        {/* Main Content Area */}
        <div className="flex-1 p-8 pr-6">
          <ProfileHeader />
          <ProfileInformation
            user={user}
            profile={profile}
            profileForm={profileForm}
            passwordForm={passwordForm}
            showPasswords={showPasswords}
            avatarFile={avatarFile}
            avatarPreview={avatarPreview}
            isUpdatingProfile={updateProfileMutation.isPending}
            isChangingPassword={changePasswordMutation.isPending}
            isUploadingAvatar={uploadAvatarMutation.isPending}
            onProfileFormChange={handleProfileFormChange}
            onPasswordFormChange={handlePasswordFormChange}
            onShowPasswordToggle={handleShowPasswordToggle}
            onProfileSubmit={handleProfileSubmit}
            onPasswordSubmit={handlePasswordSubmit}
            onAvatarFileChange={handleAvatarFileChange}
            onAvatarUpload={handleAvatarUpload}
          />
        </div>

        {/* Right Sidebar */}
        <ProfileSidebar
          user={user}
          profile={profile}
          avatarPreview={avatarPreview}
          onPasswordReset={handlePasswordReset}
          onAvatarUpload={() => document.getElementById('avatar-upload')?.click()}
        />
      </div>
    </motion.div>
  );
}
