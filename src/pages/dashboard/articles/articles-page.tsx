import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getArticles } from '@/services/article/article-service';
import { useAuth } from '@/stores';
import { Article } from '@/types/article';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Eye,
  FileTextIcon,
  FilterIcon,
  RefreshCw,
  SearchIcon,
} from 'lucide-react';
import { useState } from 'react';

export default function ArticlesPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Fetch articles from Google Apps Script
  const {
    data: allArticles = [],
    isLoading: articlesLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['articles', user?.id],
    queryFn: () => getArticles(user?.id || ''),
    enabled: !!user,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Filter articles based on search and status
  const filteredArticles = allArticles.filter(article => {
    const matchesSearch =
      article.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.content?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || article.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Calculate basic stats
  const publishedCount = allArticles.filter(
    a => a.status === 'published'
  ).length;
  const draftCount = allArticles.filter(a => a.status === 'draft').length;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  // Show error state if there's an error
  if (error && !articlesLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold text-foreground mb-1">Articles</h2>
          <p className="text-muted-foreground text-base">
            View all your automated blog articles
          </p>
        </div>

        <Card className="shadow-lg border-none bg-white dark:bg-zinc-900">
          <CardContent className="p-8 text-center">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Unable to load articles</CardTitle>
              <CardDescription className="text-sm">
                {error.message ||
                  'There was a problem connecting to your Google Apps Script. Please check your setup.'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => refetch()} className="mt-4">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </CardContent>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants}>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Articles
            </h1>
            <p className="text-muted-foreground text-lg">
              Manage and monitor your AI-generated content
            </p>
          </div>
          <Button
            variant="outline"
            size="lg"
            onClick={() => refetch()}
            disabled={articlesLoading}
            className="w-full lg:w-auto shadow-md hover:shadow-lg transition-all duration-200"
          >
            <RefreshCw
              className={`mr-2 h-5 w-5 ${articlesLoading ? 'animate-spin' : ''}`}
            />
            Refresh Articles
          </Button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Articles
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {allArticles.length}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                  <FileTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-green-600 dark:text-green-400 font-medium">
                  {publishedCount} published
                </span>
                <span className="text-muted-foreground ml-1">
                  • {draftCount} drafts
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Content Status
                  </p>
                  <p className="text-2xl font-bold text-foreground">Active</p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-green-600 dark:text-green-400 font-medium">
                  Auto-publishing enabled
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Latest Update
                  </p>
                  <p className="text-2xl font-bold text-foreground">Today</p>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full">
                  <Calendar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-purple-600 dark:text-purple-400 font-medium">
                  Content synchronized
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Filters and Controls */}
      <motion.div variants={itemVariants}>
        <Card className="p-6">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Status Filter */}
              <div className="flex items-center gap-2">
                <FilterIcon className="h-4 w-4 text-muted-foreground" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Results Info */}
          <div className="flex items-center gap-6 mt-4 pt-4 border-t border-border/50">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <FileTextIcon className="h-4 w-4" />
              <span>
                {filteredArticles.length} of {allArticles.length} articles
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>{publishedCount} published</span>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Content */}
      <motion.div variants={itemVariants}>
        <ArticleCardsView
          articles={filteredArticles}
          loading={articlesLoading}
          searchTerm={searchTerm}
          statusFilter={statusFilter}
        />
      </motion.div>
    </motion.div>
  );
}

// Cards View Component
function ArticleCardsView({
  articles,
  loading,
  searchTerm,
  statusFilter,
}: {
  articles: Article[];
  loading: boolean;
  searchTerm: string;
  statusFilter: string;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const articlesPerPage = 12;

  const totalPages = Math.ceil(articles.length / articlesPerPage);
  const startIndex = (currentPage - 1) * articlesPerPage;
  const endIndex = startIndex + articlesPerPage;
  const currentArticles = articles.slice(startIndex, endIndex);

  return (
    <>
      <Card className="shadow-lg border-none">
        <CardContent className="p-6">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="w-full h-48 bg-muted rounded-xl mb-4"></div>
                  <div className="h-5 bg-muted rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-muted rounded w-full mb-2"></div>
                  <div className="h-4 bg-muted rounded w-2/3 mb-3"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-4 bg-muted rounded w-1/3"></div>
                    <div className="h-6 bg-muted rounded-full w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : articles.length === 0 ? (
            <div className="text-center py-16">
              <div className="relative p-6 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full w-32 h-32 mx-auto flex items-center justify-center mb-6">
                <FileTextIcon className="h-16 w-16 text-primary" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">
                {searchTerm || statusFilter !== 'all'
                  ? 'No articles found'
                  : 'No articles yet'}
              </h3>
              <p className="text-muted-foreground max-w-md mx-auto text-lg">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : "Your AI-generated articles will appear here once they're created"}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentArticles.map((article, index) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -4 }}
                  className="bg-card border border-border/50 rounded-xl overflow-hidden hover:shadow-xl hover:border-primary/30 transition-all duration-300 cursor-pointer group"
                >
                  {/* Article Image */}
                  <div className="relative w-full h-48 bg-muted overflow-hidden">
                    {article.featured_image ? (
                      <img
                        src={article.featured_image}
                        alt={article.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        onError={e => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-muted-foreground bg-gradient-to-br from-muted to-muted/50">
                        <FileTextIcon className="h-12 w-12" />
                      </div>
                    )}
                    {/* SEO Score Badge */}
                    {article.seo_score && (
                      <div className="absolute top-3 right-3">
                        <Badge
                          variant="secondary"
                          className={`${
                            article.seo_score >= 80
                              ? 'bg-green-100 text-green-700 border-green-200'
                              : article.seo_score >= 60
                                ? 'bg-yellow-100 text-yellow-700 border-yellow-200'
                                : 'bg-red-100 text-red-700 border-red-200'
                          } font-medium`}
                        >
                          SEO {article.seo_score}%
                        </Badge>
                      </div>
                    )}
                  </div>

                  {/* Article Content */}
                  <div className="p-6">
                    <h3 className="font-bold text-lg mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-200">
                      {article.title}
                    </h3>

                    {/* Description/Excerpt */}
                    {(article.excerpt || article.content) && (
                      <p className="text-muted-foreground text-sm line-clamp-3 mb-4 leading-relaxed">
                        {article.excerpt ||
                          (article.content
                            ? article.content
                                .replace(/<[^>]*>/g, '')
                                .substring(0, 150) + '...'
                            : '')}
                      </p>
                    )}

                    {/* Meta Information */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        {article.published_at && (
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            <span>
                              {new Date(
                                article.published_at
                              ).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric',
                              })}
                            </span>
                          </div>
                        )}

                        {article.views !== undefined && (
                          <div className="flex items-center gap-1">
                            <Eye className="w-4 h-4" />
                            <span>{article.views.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Status and Actions */}
                    <div className="flex items-center justify-between">
                      <Badge
                        variant={
                          article.status === 'published'
                            ? 'default'
                            : article.status === 'draft'
                              ? 'secondary'
                              : 'outline'
                        }
                        className={`text-sm font-medium ${
                          article.status === 'published'
                            ? 'bg-green-100 text-green-700 border-green-200'
                            : article.status === 'draft'
                              ? 'bg-yellow-100 text-yellow-700 border-yellow-200'
                              : 'bg-blue-100 text-blue-700 border-blue-200'
                        }`}
                      >
                        {article.status}
                      </Badge>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination for Cards View */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <Button
                key={page}
                variant={currentPage === page ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              setCurrentPage(prev => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </>
  );
}
