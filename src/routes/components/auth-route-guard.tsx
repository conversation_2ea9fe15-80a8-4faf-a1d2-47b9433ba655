import { useAuth } from '@/stores';
import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

interface AuthRouteGuardProps {
  children: React.ReactNode;
}

export default function AuthRouteGuard({ children }: AuthRouteGuardProps) {
  const { user, profile, loading, isAuthenticated, getRedirectPath } =
    useAuth();
  const location = useLocation();

  useEffect(() => {
    // This effect runs when auth state changes
    // The navigation logic is handled in the render
  }, [user, profile, loading]);

  // Show loading while initializing auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <div className="space-y-2">
            <div className="text-2xl md:text-3xl font-bold text-primary">
              SEO<span className="text-primary">45</span>
            </div>
            <p className="text-muted-foreground text-sm md:text-base">
              Loading your dashboard...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If not authenticated and not on auth page, redirect to auth
  if (!isAuthenticated && location.pathname !== '/auth') {
    return <Navigate to="/auth" replace />;
  }

  // If authenticated and on auth page, redirect to appropriate dashboard
  if (isAuthenticated && location.pathname === '/auth') {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  // If authenticated and on root path, redirect to appropriate dashboard
  if (isAuthenticated && location.pathname === '/') {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
}
