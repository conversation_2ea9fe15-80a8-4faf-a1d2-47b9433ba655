import { useAuth } from '@/stores';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export default function ProtectedRoute({
  children,
  adminOnly = false,
}: ProtectedRouteProps) {
  const { user, loading, isAuthenticated, isAdmin } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <div className="space-y-2">
            <div className="text-2xl md:text-3xl font-bold text-primary">
              SEO<span className="text-primary">45</span>
            </div>
            <p className="text-muted-foreground text-sm md:text-base">
              Loading your dashboard...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Check admin access for admin-only routes
  if (adminOnly && !isAdmin()) {
    // Non-admin trying to access admin route, redirect to dashboard
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
